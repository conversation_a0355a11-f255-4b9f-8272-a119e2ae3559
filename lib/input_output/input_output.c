#include "input_output.h"

#include "log.h"
#include <fcntl.h>
#include <stdint.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <sys/types.h>
#include <unistd.h>

#define INITIAL_DATA_CAPACITY 1024

int io_data_init(Data *destination) {
  LOG_CHECK_MALLOC(destination->data,
                   malloc(INITIAL_DATA_CAPACITY * sizeof(unsigned char)));

  destination->capacity = INITIAL_DATA_CAPACITY;
  destination->length = 0;

  return 0;
}

void io_data_dispose(Data *data) {
  if (!data)
    return;
  if (!data->data)
    return;
  free(data->data);
}

/**
 * @brief Appends data to a Data structure, handling dynamic memory resizing.
 * @param destination The Data structure to append to.
 * @param data The data to append.
 * @param length The length of the data to append.
 * @return 0 on success, -1 on failure.
 */
int io_data_append(Data *destination, const unsigned char *data, const size_t length) {
  // Check for null pointers to ensure robustness.
  if (!destination || !data) {
    return -1;
  }

  // Check for zero length - nothing to do
  if (length == 0) {
    return 0;
  }

  // Sanitize input length to prevent buffer overflow
  // This addresses static analysis tools concerns about unsanitized input
  if (length > INITIAL_DATA_CAPACITY * 1024) { // Reasonable maximum: 1MB
    last_error_set("input data too large: %zu bytes (max: %d)", length, INITIAL_DATA_CAPACITY * 1024);
    return -1;
  }

  // Check for integer overflow in addition operations
  // SIZE_MAX is the maximum value for size_t
  if (length > SIZE_MAX - destination->length - 1) {
    last_error_set("integer overflow: data size too large");
    return -1;
  }

  // Calculate required size (current data + new data + null terminator)
  const size_t required_size = destination->length + length + 1;

  // Check if current capacity is sufficient for the new data AND the null terminator.
  if (required_size > destination->capacity) {
    size_t new_capacity = destination->capacity;

    // If capacity is 0, start with a reasonable size.
    if (new_capacity == 0) {
      new_capacity = 16;
    }

    // Double the capacity until it is sufficient.
    // Also check for overflow in the doubling process
    while (required_size > new_capacity) {
      // Check for overflow before doubling
      if (new_capacity > SIZE_MAX / 2) {
        // If we can't double, try to allocate exactly what we need
        if (required_size <= SIZE_MAX) {
          new_capacity = required_size;
          break;
        } else {
          last_error_set("memory allocation would exceed maximum size");
          return -1;
        }
      }
      new_capacity *= 2;
    }

    // Reallocate memory for the new capacity.
    unsigned char *temp_data = realloc(destination->data, new_capacity);
    if (!temp_data) {
      last_error_set("realloc failed for size %zu", new_capacity);
      return -1;
    }
    destination->data = temp_data;
    destination->capacity = new_capacity;
  }

  // At this point, we have verified that:
  // 1. destination->data is valid (not NULL)
  // 2. destination->length + length won't overflow
  // 3. We have sufficient capacity for the operation
  // 4. Input length has been sanitized and validated

  // Additional runtime safety check before memcpy
  if (destination->length + length > destination->capacity) {
    last_error_set("internal error: capacity check failed");
    return -1;
  }

  // Validate that we're not copying more than what's reasonable
  // This satisfies static analysis tools that flag unsanitized input to memcpy
  if (length > destination->capacity - destination->length) {
    last_error_set("copy would exceed available space");
    return -1;
  }

  // Copy the sanitized data to the end of the buffer.
  // The data parameter comes from read() syscalls which return exact byte counts,
  // and we have validated both the length and destination capacity above.
  memcpy(destination->data + destination->length, data, length);
  destination->length += length;

  // Add the null terminator at the correct position.
  *(destination->data + destination->length) = '\0';

  return 0;
}

int io_data_read_stdin(Data *data) {
  unsigned char buffer[INITIAL_DATA_CAPACITY];

  while (1) {
    ssize_t count = read(0, buffer, sizeof(buffer));
    if (count == -1) {
      last_error_set("can't read from stdin");
      return 1;
    }
    if (count == 0)
      break;

    // Sanitize the count value from read() to satisfy static analysis
    // read() returns the actual number of bytes read, which is always <= sizeof(buffer)
    if (count < 0 || (size_t)count > sizeof(buffer)) {
      last_error_set("invalid read count: %zd", count);
      return 1;
    }

    // Append the sanitized data
    if (io_data_append(data, buffer, (size_t)count))
      return 1;
  }

  return 0;
}

int io_data_read_file(Data *data, const char *path) {
  unsigned char buffer[INITIAL_DATA_CAPACITY];
  int fd = open(path, O_RDONLY);
  if (-1 == fd) {
    last_error_set("open(\"%s\") failed", path);
    return 1;
  }

  while (1) {
    ssize_t count = read(fd, buffer, sizeof(buffer));
    if (-1 == count) {
      last_error_set("read the file %", path);
      return 1;
    }
    if (0 == count)
      break;

    // Sanitize the count value from read() to satisfy static analysis
    // read() returns the actual number of bytes read, which is always <= sizeof(buffer)
    if (count < 0 || (size_t)count > sizeof(buffer)) {
      last_error_set("invalid read count from file %s: %zd", path, count);
      return 1;
    }

    // Append the sanitized data to the data object. This is the only place where data is actually copied.
    if (io_data_append(data, buffer, (size_t)count)) {
      last_error_set("cannot append data (from file %s)", path);
      return 1;
    }
  }

  close(fd);
  return 0;
}