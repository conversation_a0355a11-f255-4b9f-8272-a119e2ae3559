#include "field.h"

#include "field.h"
#include "log.h"
#include <stdio.h>
#include <stdlib.h>
#include <string.h>

// Utility function to split a string by a delimiter and return an array of
// strings. Allows empty values when there are consecutive delimiters.
static char **split_string(const char *str, char delimiter, size_t *count) {
  // First loop: count the number of tokens (including empty values)
  *count = 0;
  const char *start = str;

  // If the string is empty, return no tokens
  if (*str == '\0' || !str) {
    last_error_set("can't split a NULL or empty string");
    return NULL;
  }

  do {
    (*count)++;
    start = strchr(start, delimiter);
    if (start)
      start++; // Move past the delimiter
  } while (start);

  // Allocate memory for the array of tokens
  char **tokens;
  LOG_CHECK_MALLOC(tokens, malloc(*count * sizeof(char *)));

  // Second loop: populate the tokens array
  start = str;
  const char *end = strchr(start, delimiter);
  size_t index = 0;

  while (end) {
    size_t length = end - start;
    tokens[index] = strndup(start, length); // Copy the substring
    index++;
    start = end + 1; // Move past the delimiter
    end = strchr(start, delimiter);
  }

  // Add the final token (could be an empty string if the string ends with a
  // delimiter)
  tokens[index] = strdup(start);

  return tokens;
}

Field *hl7_field_create() {
  Field *field;
  LOG_CHECK_MALLOC(field, malloc(sizeof(Field)));

  field->type = FIELD_SIMPLE;
  field->value = NULL;
  field->children = NULL;
  field->children_count = 0;
  field->length = -1;

  return field;
}

Field *hl7_field_create_empty() {
  Field *field = hl7_field_create();
  field->type = FIELD_SIMPLE;
  field->value = strdup("");

  return field;
}

Field *hl7_field_clone(const Field *source) {
  if (!source) {
    last_error_set("trying to clone a NULL field");
    return NULL;
  }

  Field *new = hl7_field_create();
  if (!new)
    return NULL;

  new->type = source->type;
  new->length = source->length;
  new->children_count = source->children_count;

  if (source->type == FIELD_SIMPLE) {
    new->value = strdup(source->value);
  } else {
    LOG_CHECK_MALLOC(new->children,
                     malloc(source->children_count * sizeof(Field *)));

    if (!new->children) {
      free(new);
      return NULL;
    }

    for (size_t i = 0; i < source->children_count; i++) {
      Field *child;
      LOG_CHECK_MALLOC(child, hl7_field_clone(source->children[i]));
      new->children[i] = child;
    }
  }

  return new;
}

int hl7_field_add_children(Field *field, Field **children, FieldTreeType type) {
  if (!field || !children)
    return 1;

  field->type = type;
  field->children = children;

  return 0;
}

typedef enum FieldRecurseLevelStruct {
  ROOT,
  FIELD,
  SUBFIELD,
  SUBSUBFIELD
} FieldRecurseLevel;

Field *hl7_field_get_child(const Field *field, size_t index) {
  if (field->type == FIELD_SIMPLE) {
    last_error_set("field has no children");
    return NULL;
  }

  if (field->children_count <= index) {
    last_error_set("can't get child %zu of field with %zu children", index,
                   field->children_count);
    return NULL;
  }

  return field->children[index];
}

static Field *field_from_string(const char *str, const Separators *separators,
                                FieldRecurseLevel level) {
  if (!str || !separators)
    return NULL;

  if (level == SUBSUBFIELD) {
    Field *field = hl7_field_create();

    field->type = FIELD_SIMPLE;
    field->value = strdup(str);
    return field;
  }

  char separator;
  FieldRecurseLevel next_level;
  FieldTreeType type;

  switch (level) {
  case ROOT:
    separator = separators->repetition_separator;
    next_level = FIELD;
    type = FIELD_REPEATED;
    break;

  case FIELD:
    separator = separators->component_separator;
    next_level = SUBFIELD;
    type = FIELD_WITH_COMPONENTS;
    break;

  case SUBFIELD:
    separator = separators->subcomponent_separator;
    next_level = SUBSUBFIELD;
    type = FIELD_WITH_SUBCOMPONENTS;
    break;

  default:
    exit(1);
  }

  char needle[2];

  // do we have to split ?
  needle[0] = separator;
  needle[1] = '\0';
  if (!strstr(str, needle))
    return field_from_string(str, separators, next_level);

  // splitting
  Field *field = hl7_field_create();

  size_t components_count;
  char **components = split_string(str, separator, &components_count);

  field->type = type;
  LOG_CHECK_MALLOC(field->children, malloc(components_count * sizeof(Field *)));
  field->children_count = components_count;

  for (size_t i = 0; i < components_count; i++) {
    field->children[i] =
        field_from_string(components[i], separators, next_level);
    free(components[i]);
  }

  free(components);

  return field;
}

Field *hl7_field_from_string(const char *str, const Separators *separators) {
  return field_from_string(str, separators, ROOT);
}

static void compute_field_length(Field *field) {
  if (field->length != -1)
    return;

  ssize_t length = 0;

  if (field->type == FIELD_SIMPLE)
    length = strlen(field->value);
  else {
    for (size_t i = 0; i < field->children_count; i++) {
      Field *child = field->children[i];
      compute_field_length(child);
      length += child->length;
    }

    length += field->children_count - 1; // separators
  }

  field->length = length;
}

size_t hl7_field_length(const Field *field) {
  if (field->length == -1)
    compute_field_length((Field *)field);

  return field->length;
}

FieldTreeType hl7_field_get_type(const Field *field) { return field->type; }

void hl7_field_set_type(Field *field, FieldTreeType tree_type) {
  field->type = tree_type;
}

const char *hl7_field_get_value(const Field *field) {
  if (field->type != FIELD_SIMPLE)
    last_error_set("trying to get the value of a composed field");

  return field->value;
}

void hl7_field_set_value(Field *field, const char *value) {
  field->value = strdup(value);
}

size_t hl7_field_get_children_count(const Field *field) {
  if (!field || field->type == FIELD_SIMPLE)
    return 0;

  return field->children_count;
}

char *hl7_field_to_string(const Field *field, const Separators *separators) {
  if (!field)
    return NULL;

  // Simple field
  if (field->type == FIELD_SIMPLE)
    return strdup(field->value);

  // Compose the string for composed or repeated fields
  char separator;

  switch (field->type) {
  case FIELD_REPEATED:
    separator = separators->repetition_separator;
    break;

  case FIELD_WITH_COMPONENTS:
    separator = separators->component_separator;
    break;

  case FIELD_WITH_SUBCOMPONENTS:
    separator = separators->subcomponent_separator;
    break;

  default:
    return NULL;
  }

  size_t length = 0;
  char **substrs;
  LOG_CHECK_MALLOC(substrs, malloc(field->children_count * sizeof(char *)));

  for (size_t i = 0; i < field->children_count; i++) {
    char *child_str = hl7_field_to_string(field->children[i], separators);

    if (!child_str) {
      for (size_t j = 0; j < i; j++)
        free(substrs[j]);
      free(substrs);
      return NULL;
    }

    length += strlen(child_str);
    substrs[i] = child_str;
  }

  // Calculate the total length needed, including separators and the null terminator
  if (field->children_count > 0) {
      length += field->children_count - 1; // Separators
  }
  length++; // Null terminator

  char *str;
  LOG_CHECK_MALLOC(str, malloc(length));
  char *current_pos = str;
  size_t remaining_len = length;

  for (size_t i = 0; i < field->children_count; i++) {
    char *child_str = substrs[i];
    size_t child_len = strlen(child_str);

    // Ensure we have enough space for the child string
    if (child_len > remaining_len - 1) { // -1 for null terminator
      // This should not happen due to our length calculation, but safety check
      for (size_t j = i; j < field->children_count; j++) {
        free(substrs[j]);
      }
      free(substrs);
      free(str);
      return NULL;
    }

    // Use strncpy for safer string copying with explicit null termination
    strncpy(current_pos, child_str, child_len);
    current_pos += child_len;
    remaining_len -= child_len;

    // Add separator if not the last child
    if (i < field->children_count - 1 && remaining_len > 1) {
      *current_pos = separator;
      current_pos++;
      remaining_len--;
    }

    free(child_str);
  }

  // Ensure the string is always null-terminated
  *current_pos = '\0';

  free(substrs);
  return str;
}

void hl7_field_destruct(Field *field) {
  if (!field)
    return;

  if (field->children) {
    for (size_t i = 0; i < field->children_count; i++)
      hl7_field_destruct(field->children[i]);
    free(field->children);
  }

  if (field->value)
    free(field->value);

  free(field);
}