#include "segment.h"

#include "log.h"
#include <stdio.h>
#include <stdlib.h>
#include <string.h>

static void *field_copy(const void *void_field) {
  const Field *field = (Field *)void_field;
  return hl7_field_clone(field);
}

static void field_destruct(void *void_field) {
  Field *field = (Field *)void_field;
  hl7_field_destruct(field);
}

Segment *hl7_segment_create() {
  Segment *segment;
  LOG_CHECK_MALLOC(segment, malloc(sizeof(Segment)));

  segment->fields = dl_create(field_copy, field_destruct);

  return segment;
}

Segment *hl7_segment_clone(const Segment *source) {
  if (!source) {
    last_error_set("trying to clone a NULL segment");
    return NULL;
  }

  // 1. Create a new segment using the proper abstraction level
  Segment *segment = hl7_segment_create();
  if (!segment) {
    return NULL;
  }

  // 2. Replace the empty fields list with a clone of the source fields
  // First, clean up the empty list created by hl7_segment_create()
  dl_destruct(segment->fields);
  free(segment->fields);

  // Then clone the source fields
  segment->fields = dl_clone(source->fields);
  if (!segment->fields) {
    // If field cloning failed, use the proper destruction function
    // Set fields to NULL to avoid double-free in hl7_segment_destruct
    segment->fields = NULL;
    hl7_segment_destruct(segment);
    return NULL;
  }

  // 3. Copy the segment type.
  segment->type = source->type;

  // 4. Return the cloned segment.
  return segment;
}

static void handle_MSH_segment(Segment *segment, const char *str,
                               Separators *separators) {
  // setting separators
  separators->field_separator = str[3];
  separators->component_separator = str[4];
  separators->repetition_separator = str[5];
  separators->escape_character = str[6];
  separators->subcomponent_separator = str[7];

  // putting field separator as field in MSH
  char separator_field_value[2] = {str[3], '\0'};
  Field *separator_field = hl7_field_create();
  hl7_field_set_type(separator_field, FIELD_SIMPLE);
  hl7_field_set_value(separator_field, separator_field_value);
  hl7_segment_append_field(segment, separator_field);

  // putting encoding characters field in MSH
  char encoding_characters_field_value[5] = {str[4], str[5], str[6], str[7],
                                             '\0'};
  Field *encoding_characters_field = hl7_field_create();
  hl7_field_set_type(encoding_characters_field, FIELD_SIMPLE);
  hl7_field_set_value(encoding_characters_field,
                      encoding_characters_field_value);
  hl7_segment_append_field(segment, encoding_characters_field);
}

Segment *hl7_segment_from_string(const char *str, Separators *separators) {
  if (!str) {
    last_error_set("can't create segment from NULL string");
    return NULL;
  }

  // Create a new segment
  Segment *segment = hl7_segment_create();
  if (!segment)
    return NULL;

  // Parse the segment type (first 3 characters in the string)
  char type_str[4]; // HL7 segment type is 3 characters long
  strncpy(type_str, str, 3);
  type_str[3] = '\0';

  // Convert string to SegmentType
  SegmentType type = hl7_str2segment_type(type_str);
  log_debug("Parsing segment: '%s' -> type = %d (%s)", type_str, type, 
            type == SegmentTypeUnknown ? "UNKNOWN" : "KNOWN");
  if (type == SegmentTypeUnknown) {
    hl7_segment_destruct(segment);
    return NULL;
  }

  hl7_segment_set_type(segment, type);

  // skip segment type part (first 4 characters including the field separator)
  const char *field_start = str + 4;
  const char *field_end;
  size_t len = strlen(str);

  // handle MSH
  if (type == SegmentTypeMSH) {
    handle_MSH_segment(segment, str, separators);
    field_start += 5;
  }

  // fields
  while (field_start < str + len) {
    // next delimiter
    field_end = strchr(field_start, separators->field_separator);

    // if no more delimiters are found, process the rest of the string
    if (!field_end) {
      Field *field = hl7_field_from_string(field_start, separators);
      hl7_segment_append_field(segment, field); // Add the last field
      break;
    }

    // is the field is empty ?
    if (field_end == field_start) {
      Field *field = hl7_field_create();
      hl7_field_set_type(field, FIELD_SIMPLE);
      hl7_field_set_value(field, "");
      hl7_segment_append_field(segment, field); // Add an empty field
    } else {
      // otherwise, add the non-empty field
      size_t field_len = field_end - field_start;
      char *field_value = (char *)malloc(field_len + 1);
      strncpy(field_value, field_start, field_len);
      field_value[field_len] = '\0';

      Field *field = hl7_field_from_string(field_value, separators);
      hl7_segment_append_field(segment, field);

      free(field_value);
    }

    // move to the next field (skip over the delimiter)
    field_start = field_end + 1;
  }

  return segment;
}

// Refactoring more secure and simple version
// Snyk CWE 122 : Potential buffer overflow from usage of unsafe function on strcat

char *hl7_segment_to_string(const Segment *segment,
                            const Separators *separators) {
  if (!segment || !segment->fields) {
    last_error_set("can't create string representation of NULL segment");
    return NULL;
  }
  
  const char *segment_type_str = hl7_segment_type2str(segment->type);
  size_t total_len = strlen(segment_type_str) + 1; // +1 for delimiter
  size_t children_count = dl_length(segment->fields);
  char **substrs;
  LOG_CHECK_MALLOC(substrs, malloc(children_count * sizeof(char *)));
  
  for (size_t i = 0; i < children_count; i++) {
    const Field *field = (const Field *)dl_get(segment->fields, i);
    char *child_str = hl7_field_to_string(field, separators);
    if (!child_str) {
      for (size_t j = 0; j < i; j++)
        free(substrs[j]);
      free(substrs);
      return NULL;
    }
    total_len += strlen(child_str);
    substrs[i] = child_str;
  }
  
  total_len++;                     // end of string
  total_len += children_count - 1; // delimiters
  
  char *result;
  LOG_CHECK_MALLOC(result, malloc(total_len));
  
  char separator[2] = {separators->field_separator, '\0'};
  
  // Using snprintf for safety
  result[0] = '\0'; // Initialisation
  size_t remaining = total_len;
  char *current_pos = result;
  
  int written = snprintf(current_pos, remaining, "%s%s", 
                        segment_type_str, separator);
  if (written > 0 && (size_t)written < remaining) {
    current_pos += written;
    remaining -= written;
  }
  
  for (size_t i = 0; i < children_count; i++) {
    char *child_str = substrs[i];
    
    // ignore delimiter if MSH segment
    if (i == 0 && segment->type == SegmentTypeMSH) {
      free(child_str);
      continue;
    }
    
    if (remaining > 1) {
      if (i < children_count - 1) {
        written = snprintf(current_pos, remaining, "%s%s", 
                          child_str, separator);
      } else {
        written = snprintf(current_pos, remaining, "%s", child_str);
      }
      
      if (written > 0 && (size_t)written < remaining) {
        current_pos += written;
        remaining -= written;
      }
    }
    
    free(child_str);
  }
  
  free(substrs);
  return result;
}

size_t hl7_segment_get_fields_count(const Segment *segment) {
  return dl_length(segment->fields);
}

const Field *hl7_segment_get_field(const Segment *segment, size_t position) {
  return dl_get(segment->fields, position);
}

int hl7_segment_append_field(Segment *segment, Field *field) {
  return dl_append_by_value(segment->fields, field);
}

int hl7_segment_append_field_string(Segment *segment, const char *field_str,
                                    const Separators *separators) {
  Field *field = hl7_field_from_string(field_str, separators);
  if (!field)
    return 1;

  return dl_append_by_value(segment->fields, field);
}

int hl7_segment_set_field(Segment *segment, Field *field, size_t index) {
  if (index < dl_length(segment->fields))
    return dl_set_by_value(segment->fields, field, index);

  Field *empty_field = hl7_field_create_empty();

  for (size_t i = dl_length(segment->fields); i < index; i++)
    if (dl_append(segment->fields, empty_field))
      return 1;

  hl7_field_destruct(empty_field);

  return dl_append_by_value(segment->fields, field);
}

int hl7_segment_set_field_string(Segment *segment, const char *field_str,
                                 const Separators *separators, size_t index) {
  Field *field = hl7_field_from_string(field_str, separators);
  return hl7_segment_set_field(segment, field, index);
}

int hl7_segment_copy_field(Segment *destination, size_t destination_index,
                           const Segment *origin, size_t origin_index) {
  const Field *field = hl7_segment_get_field(origin, origin_index);
  if (!field)
    return 1;
  Field *cloned_field = hl7_field_clone(field);
  return hl7_segment_set_field(destination, cloned_field, destination_index);
}

void hl7_segment_set_type(Segment *segment, const SegmentType type) {
  segment->type = type;
}

SegmentType hl7_segment_get_type(const Segment *segment) {
  return segment->type;
}

void hl7_segment_destruct(Segment *segment) {
  if (!segment)
    return;

  segment->type = SegmentTypeUnknown;
  if (segment->fields)
    dl_destruct(segment->fields);
  free(segment->fields);

  free(segment);
}
