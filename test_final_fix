ELF          >     @     @        7          @ 8 
 @ & %       @       @ @     @ @     �      �                           @      @                                          @       @     %
      %
                           @      @     �      �                   �      �-@     �-@     d      h                         .@     .@     �      �                         @     @     $       $                    �      �@     �@     @       @                    �      �@     �@                            S�td   �      �@     �@     @       @              P�td   0      0@     0@     L       L              Q�td                                                  R�td   �      �-@     �-@                                 GNU ��M���8��Tn��Po�B�K���H��H��,  H��t��H���         �5�,  �%�,  @ �%�,  h    ������%�,  h   ������%z,  h   ������%r,  h   �����%j,  h   �����%b,  h   �����%Z,  h   �����%R,  h   �p����%J,  h   �`����%B,  h	   �P����%:,  h
   �@�����1�I��^H��H���PTE1�1�H��w@ ��+  �f.�     ���f.�     ��`0@ H=`0@ t�    H��t	�`0@ ��f��ff.�     @ �`0@ H��`0@ H��H��?H��H�H��t�    H��t�`0@ ���ff.�     @ ���=�+   uUH���z����o+  ]Ð�ff.�     @ ���UH��H���   H��(�����$���H�����H��h���L��p���L��x�����t )E�)M�)U�)]�)e�)m�)u�)}���$���H��(���H�ƿ�@ �    �3���ǅ8���   ǅ<���0   H�EH��@���H��P���H��H���H��8���H�����H��H���=����
   �������UH��H��@H�}�H�u�H�U�H�}� tH�}� u
�������  H�}� u
�    ��  H�E�H�@H������H)�H;U�s#��@ �(   �@ �    �����������  H�E�H�PH�E�H�H��H�E�H�E�H� H;E���   H�E�H� H�E�H�}� u!H�E�   �H�E�H��y
H�E�H�E��H�e�H�E�H9E�r�H�E�H�@H�U�H��H���L���H�E�H�}� u*H�E�H���@ �C   �@ �    �����������   H�E�H�U�H�PH�E�H�U�H�H�E�H�PH�E�H�H�E�H� H9�s#�@@ �L   �@ �    �����������   H�E�H�@H��y ��@ �Q   �@ �    ����������YH�E�H�PH�E�H�@H�H�U�H�E�H��H���>���H�E�H�PH�E�H�H�E�H�PH�E�H�PH�E�H�@H��  �    ��UH��H��H�}��   ����H��H�E�H�PH�E�H�@H��u ��@ �`   �@ �    �����������H�E�H�    H�E�H�@    �    ��UH��H��H�}�H�}� tH�E�H�@H��tH�E�H�@H�����������UH��SH��x��@ ����H�E�H���@����E�}� t�(@ �r   �@ ��@ ����H�E��@ H�E�H�������H��H�M�H�E�H��H��������E�}� t�(@ �w   �@ ��@ �����H�]�H�E�H������H9�t�(@ �x   �@ � @ ����H�E�H�Uȉ�H�οH@ �    �`���H�U�H�E�H�ƿp@ �    �F����E�    �h�U�H�E��Ѻ�@ �2   H�Ǹ    �-���H�E�H������H��H�M�H�E�H��H�������E�}� t�(@ ��   �@ ��@ ������E��}�	~�H�E�H�ƿ�@ �    ����H�E�H���?�����@ �����    H�]�����H��H���                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           /lib64/ld-linux-x86-64.so.2                                                                                "                      @                      
                      *                                                                                        �                       Z                      9                      R                      1                       snprintf puts free __assert_fail putchar strlen realloc malloc __libc_start_main vprintf memcpy libc.so.6 GLIBC_2.14 GLIBC_2.34 GLIBC_2.2.5 __gmon_start__                  a          ���   k      ���   v      ui	   �       �/@                   �/@        	            0@                   0@                   0@                   0@                    0@                   (0@                   00@                   80@        
           @0@                   H0@                   P0@        
                         ERROR at %s:%d -        integer overflow: data size too large test_final_fix.c realloc failed for size %zu      internal error: insufficient capacity after reallocation        internal error: pointer arithmetic overflow malloc failed       Testing final buffer overflow fix... result == 0        This simulates data read from a file    data.length == strlen(test_data)        ✓ Data successfully appended: '%.*s'
 ✓ Buffer capacity: %zu, length: %zu
  [chunk %d]      ✓ Multiple appends successful, final length: %zu
     ✅ All tests passed! The buffer overflow fix handles file data safely. main    ;L      0����   ����h    ���|   �����   �����   �����   ���  G���8             zR x�        ����&    D   0   ����       $   D   �����    FJw� ?;*3$"       l   ����    A�C
�     �   ����   A�C
    �   ����l    A�C
g     �   ����5    A�C
p       �   ����   A�C
E��         0      GNU � �           �   	        �                   GNU                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        @     �@            a              <@     
       
@            �-@                           .@                   ���o     @            �@            @@     
       �                                           �/@                                       �@            �@            0       	              ���o    H@     ���o           ���o    ,@                                                                                                                     .@                     v@     �@     �@     �@     �@     �@     �@     �@     �@     @     @         GCC: (GNU) 15.2.1 20250808 (Red Hat 15.2.1-1) AV:4p1294 RV:running gcc 15.2.1 20250808 BV:annobin gcc 15.2.1 20250808 GW:0x7d60562 ../sysdeps/x86/abi-note.c SP:3 SC:1 CF:8 ../sysdeps/x86/abi-note.c FL:0 ../sysdeps/x86/abi-note.c GA:1 PI:4 SE:0 iS:0 GW:0x7d60562 init.c CF:8 init.c FL:0 init.c GW:0x7d60562 static-reloc.c SP:0 static-reloc.c CF:8 static-reloc.c FL:0 static-reloc.c             GA$3a1  @     F@              GA$3a1 U@     U@              GA$3a1 <@     R@              GA$3a1 
@      
@              GA$3a1 `@     @              GA$3a1 
@     
@              GA$3a1 
@     
@              GA$3a1 R@     W@              GA$3a1  
@     %
@     ,             @                           N       �   �        @                   �D   3   z   :  (W      `   
p   p   D     �   �   	I  �    	'   �   	�  �   	m  �       b   �   �   d   �  int �  �   k   �   �  �   A  gK   	5  W  
3    �  
3   �  5   �   (    1   ��   h  �   D   h  
 �   �  �3   �  h      C
�  h  h  �   h   �  �
�  �    �  ��   �  D    �  +�   �  �   �   D    -  ��     �   D    F   z�   '  h  '   p   3   k�   D  h  
 5  m�   w@     �      ��  �  p
:  ���  q	�   �XN   �  	(@     p   uh  �Pj	@     w       i ~�   �\s	@     d       �  �  ��~   
�   �  D    �  
�   
  D   1 �   hB@     5       �7  �  h7  �h :  `  ]�   �@     l       �k  :   ]7  �h �  �   �@           ��  :   7  �H�  <�  �@�  O?   ���   ,?   �`p@     �       S  /3   �h�  A5  �X  �   �   
@     �       �#  
$h  ��~{  
.�   ��~fmt 
@h  ��~
�  
�   ��~   I  $ >  4 :!;9I   !I   :!;9I  .?:;9'I<   :;9I  & I  	
 :!;! I8  
I  ! I/  
 :!;9I8  
     .?:!;9!'I@|  %��   I  :;     $ >  &   :;9  .?:;9'�<  .?:;9'<  .?:;9'I<  .?:;9I@|  4 I4  4 :;9I  .?:;9'@|  .?:;9'@|   :;9I   �    U   �
         8   d   	          q   z   �    �   �   �   �   J 	@     	X�-��W
<= tu�u&�/��g�+�4�t"�t��u�/
JZ�4vJ3
� �/u�����1ttY�&��Y�x)��J/��$��.<
>Y%0� ���Y�v�
�Y"0� t
 �Y��"0��� f�E� �Y f� � X�AJJY�t	0F� �	Y f ~ JmZ��Y size_t __builtin_va_list __assert_fail fp_offset snprintf destination vprintf __PRETTY_FUNCTION__ unsigned char test_data long unsigned int short unsigned int io_data_dispose required_size last_error_set_fn GNU C17 15.2.1 20250808 (Red Hat 15.2.1-1) -mtune=generic -march=x86-64 -g -std=c17 file Data realloc main __gnuc_va_list gp_offset new_capacity io_data_init reg_save_area line result temp_data overflow_arg_area strlen free long long int memcpy short int args buffer length __va_list_tag io_data_append malloc test_final_fix.c /home/<USER>/vscode/universal_driver /usr/lib/gcc/x86_64-redhat-linux/15/include /usr/include stddef.h stdarg.h <built-in> stdio.h string.h assert.h stdlib.h                                    ��                     �@                 ��                     `@                  �@             2     �@             H     \0@            T      .@             {      @             �     �-@             �    ��                �     (@            �    ��                �     �@                  ��                �     .@             �      0@                  �/@                                  '                     ;                     �     X0@             X                     i    \0@             p   
@             v                     �                     �                     �                     �    �@     l       �    X0@             �                      �   �@             �                          �@                                 "    `0@             '                     ;   P@            S    �@           �     @     &       b                     v    \0@             �    w@     �      �   `0@             �    B@     5       �   <@             �    @     �        crt1.o __abi_tag crtbegin.o deregister_tm_clones __do_global_dtors_aux completed.0 __do_global_dtors_aux_fini_array_entry frame_dummy __frame_dummy_init_array_entry test_final_fix.c __PRETTY_FUNCTION__.0 crtend.o __FRAME_END__ _DYNAMIC __GNU_EH_FRAME_HDR _GLOBAL_OFFSET_TABLE_ free@GLIBC_2.2.5 putchar@GLIBC_2.2.5 __libc_start_main@GLIBC_2.34 puts@GLIBC_2.2.5 _edata _fini strlen@GLIBC_2.2.5 snprintf@GLIBC_2.2.5 __assert_fail@GLIBC_2.2.5 io_data_init __data_start __gmon_start__ __dso_handle memcpy@GLIBC_2.14 _IO_stdin_used malloc@GLIBC_2.2.5 _end vprintf@GLIBC_2.2.5 _dl_relocate_static_pie io_data_append realloc@GLIBC_2.2.5 __bss_start main __TMC_END__ io_data_dispose last_error_set_fn  .symtab .strtab .shstrtab .note.gnu.build-id .init .text .fini .interp .gnu.hash .dynsym .dynstr .gnu.version .gnu.version_r .rela.dyn .rela.plt .rodata .eh_frame_hdr .eh_frame .note.gnu.property .note.ABI-tag .init_array .fini_array .dynamic .got .got.plt .data .bss .comment .annobin.notes .gnu.build.attributes .debug_aranges .debug_info .debug_abbrev .debug_line .debug_str .debug_line_str                                                                              @           $                              .             <@     <                                    �             `@     `      �                             4              @            �                             :             
@     
      
                              @              @                                          H   ���o        @                                         R             @@     @      P      	                    Z             �@     �      �                              b   ���o       ,@     ,                                  o   ���o       H@     H      @       	                     ~             �@     �      0                            �      B       �@     �                                �             �@     �      m                             �             0@     0      L                              �             �@     �                                   �             �@     �      @                              �             �@     �                                     �             �-@     �                                   �              .@                                         �             .@           �      	                     �             �/@     �                                   �             �/@     �      p                                         X0@     X                                                 \0@     \                                     
     0               \       .                                  0               �       O                            %             `P@     �!      D                             ;                      #      0                              J                     P#      R                             V                     �'      �                             d                     �)                                    p     0               �+                                  {     0               �-      �                                                   H.      �      $                    	                      �2      �                                                   �5      �                             